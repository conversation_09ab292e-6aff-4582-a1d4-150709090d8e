import { STORAGE_KEYS, API_CONFIG } from '../constants';

// LocalStorage helper funksiyaları
export const storage = {
  get: (key) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Storage get error:', error);
      return null;
    }
  },

  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  },

  remove: (key) => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  }
};

// API key idarəetməsi
export const apiKeyUtils = {
  get: () => storage.get(STORAGE_KEYS.API_KEY),
  set: (key) => storage.set(STORAGE_KEYS.API_KEY, key),
  remove: () => storage.remove(STORAGE_KEYS.API_KEY),
  isValid: (key) => key && key.trim().length > 0
};

// Sessiya ID generatoru
export const generateSessionId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Tarix formatlaşdırması
export const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now - date) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    return 'İndi';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)} saat əvvəl`;
  } else if (diffInHours < 48) {
    return 'Dünən';
  } else {
    return date.toLocaleDateString('az-AZ', {
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
};

// Mətn kəsici
export const truncateText = (text, maxLength = 50) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

// Debounce funksiyası
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle funksiyası
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Retry funksiyası
export const retry = async (fn, retries = API_CONFIG.MAX_RETRIES, delay = API_CONFIG.RETRY_DELAY) => {
  try {
    return await fn();
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
      return retry(fn, retries - 1, delay * 2);
    }
    throw error;
  }
};

// Mermaid kod təmizləyici
export const cleanMermaidCode = (code) => {
  if (!code) return '';
  
  return code
    .replace(/^```mermaid\s*/, '')
    .replace(/```\s*$/, '')
    .trim();
};

// Mermaid kodundan detalları çıxarma
export const extractDetailsFromMermaid = (mermaidCode) => {
  const details = new Map();
  const lines = mermaidCode.split('\n');
  
  for (const line of lines) {
    const commentMatch = line.match(/^%%\s*([A-Z]\d+):::(.+)$/);
    if (commentMatch) {
      const [, nodeId, content] = commentMatch;
      details.set(nodeId, content.replace(/__NL__/g, '\n'));
    }
  }
  
  return details;
};

// Xəta mesajı formatlaşdırması
export const formatError = (error) => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.error?.message) return error.error.message;
  return 'Naməlum xəta baş verdi';
};

// Responsive breakpoint yoxlayıcısı
export const getBreakpoint = () => {
  const width = window.innerWidth;
  if (width < 640) return 'sm';
  if (width < 768) return 'md';
  if (width < 1024) return 'lg';
  if (width < 1280) return 'xl';
  return '2xl';
};

// Touch cihaz yoxlayıcısı
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Clipboard funksiyası
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback üçün
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      textArea.remove();
      return result;
    }
  } catch (error) {
    console.error('Copy to clipboard failed:', error);
    return false;
  }
};

// URL validator
export const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// Keyboard shortcut handler
export const handleKeyboardShortcut = (event, shortcuts) => {
  const key = event.key.toLowerCase();
  const ctrl = event.ctrlKey || event.metaKey;
  const shift = event.shiftKey;
  const alt = event.altKey;
  
  const shortcutKey = `${ctrl ? 'ctrl+' : ''}${shift ? 'shift+' : ''}${alt ? 'alt+' : ''}${key}`;
  
  if (shortcuts[shortcutKey]) {
    event.preventDefault();
    shortcuts[shortcutKey]();
  }
};
