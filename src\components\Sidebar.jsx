import { useState } from "react";
import {
  Plus,
  Settings,
  Trash2,
  Edit3,
  Check,
  X,
  MessageSquare,
  Calendar,
  Code,
  MoreVertical,
} from "lucide-react";
import { formatDate, truncateText } from "../utils";

const Sidebar = ({
  sessions,
  activeSessionId,
  onSessionSelect,
  onNewSession,
  onDeleteSession,
  onRenameSession,
  onOpenApiModal,
  isOpen,
  onClose,
}) => {
  const [editingSessionId, setEditingSessionId] = useState(null);
  const [editingName, setEditingName] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);

  const handleStartEdit = (session) => {
    setEditingSessionId(session.id);
    setEditingName(session.name);
  };

  const handleSaveEdit = () => {
    if (editingName.trim() && onRenameSession) {
      onRenameSession(editingSessionId, editingName.trim());
    }
    setEditingSessionId(null);
    setEditingName("");
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditingName("");
  };

  const handleDeleteClick = (sessionId) => {
    setShowDeleteConfirm(sessionId);
  };

  const handleConfirmDelete = () => {
    if (showDeleteConfirm && onDeleteSession) {
      onDeleteSession(showDeleteConfirm);
    }
    setShowDeleteConfirm(null);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveEdit();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar - Always Overlay */}
      <aside
        className={`
          fixed top-0 left-0 h-full w-80 z-50
          flex flex-col transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
        `}
        style={{
          backgroundColor: "var(--surface-1)",
          borderRight: "1px solid var(--border-color)",
        }}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-lg font-semibold text-white">AI Blok-sxem</h1>
            <button
              onClick={onClose}
              className="md:hidden w-8 h-8 rounded-lg hover:bg-gray-800 flex items-center justify-center text-gray-400"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="flex gap-2">
            <button
              onClick={onNewSession}
              className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              Yeni Sessiya
            </button>
            <button
              onClick={onOpenApiModal}
              title="API Tənzimləmələri"
              className="w-10 h-10 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg flex items-center justify-center text-gray-300 transition-colors"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Sessions List */}
        <div className="flex-1 overflow-y-auto">
          {sessions.length === 0 ? (
            <div className="p-4 text-center text-gray-400">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">Hələ sessiya yoxdur</p>
              <p className="text-xs mt-1">Yeni sessiya yaradın</p>
            </div>
          ) : (
            <div className="p-2 space-y-1">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={`
                    group relative rounded-lg border transition-all duration-200
                    ${
                      activeSessionId === session.id
                        ? "bg-blue-600/20 border-blue-500/50"
                        : "bg-gray-800/50 border-gray-700 hover:bg-gray-800 hover:border-gray-600"
                    }
                  `}
                >
                  {editingSessionId === session.id ? (
                    // Edit Mode
                    <div className="p-3">
                      <input
                        type="text"
                        value={editingName}
                        onChange={(e) => setEditingName(e.target.value)}
                        onKeyPress={handleKeyPress}
                        onBlur={handleSaveEdit}
                        autoFocus
                        className="w-full bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                      />
                      <div className="flex gap-1 mt-2">
                        <button
                          onClick={handleSaveEdit}
                          className="flex-1 px-2 py-1 bg-green-600 hover:bg-green-500 text-white rounded text-xs transition-colors"
                        >
                          <Check className="w-3 h-3 mx-auto" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="flex-1 px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white rounded text-xs transition-colors"
                        >
                          <X className="w-3 h-3 mx-auto" />
                        </button>
                      </div>
                    </div>
                  ) : showDeleteConfirm === session.id ? (
                    // Delete Confirmation
                    <div className="p-3">
                      <p className="text-sm text-white mb-2">
                        Silmək istədiyinizdən əminsiniz?
                      </p>
                      <div className="flex gap-2">
                        <button
                          onClick={handleConfirmDelete}
                          className="flex-1 px-2 py-1 bg-red-600 hover:bg-red-500 text-white rounded text-xs transition-colors"
                        >
                          Sil
                        </button>
                        <button
                          onClick={handleCancelDelete}
                          className="flex-1 px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white rounded text-xs transition-colors"
                        >
                          Ləğv et
                        </button>
                      </div>
                    </div>
                  ) : (
                    // Normal Mode
                    <div
                      className="p-3 cursor-pointer"
                      onClick={() => onSessionSelect(session.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-white truncate">
                            {session.name}
                          </h3>
                          {session.description && (
                            <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                              {truncateText(session.description, 60)}
                            </p>
                          )}
                          <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {formatDate(session.updatedAt)}
                            </div>
                            {session.language && (
                              <div className="flex items-center gap-1">
                                <Code className="w-3 h-3" />
                                {session.language}
                              </div>
                            )}
                            {session.conversationHistory?.length > 0 && (
                              <div className="flex items-center gap-1">
                                <MessageSquare className="w-3 h-3" />
                                {session.conversationHistory.length}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStartEdit(session);
                            }}
                            title="Adını dəyiş"
                            className="w-6 h-6 rounded hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                          >
                            <Edit3 className="w-3 h-3" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(session.id);
                            }}
                            title="Sil"
                            className="w-6 h-6 rounded hover:bg-red-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700">
          <p className="text-xs text-gray-500 text-center">
            AI Blok-sxem Generatoru v1.0
          </p>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
