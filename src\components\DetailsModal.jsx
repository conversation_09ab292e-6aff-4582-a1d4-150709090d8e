import { X, Copy, CheckCircle } from 'lucide-react';
import { useState } from 'react';
import { copyToClipboard } from '../utils';

const DetailsModal = ({ isOpen, onClose, nodeId, details }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  if (!isOpen || !nodeId || !details) return null;

  const nodeDetails = details.get(nodeId);
  if (!nodeDetails) return null;

  // Detalları parse et
  const parseDetails = (detailText) => {
    const parts = detailText.split('```');
    if (parts.length >= 3) {
      const description = parts[0].trim();
      const language = parts[1].split('\n')[0].trim();
      const code = parts[1].substring(language.length).trim();
      
      return {
        description,
        language,
        code: code || 'Kod nümunəsi mövcud deyil'
      };
    }
    
    return {
      description: detailText,
      language: '',
      code: ''
    };
  };

  const { description, language, code } = parseDetails(nodeDetails);

  const handleCopyCode = async () => {
    if (code) {
      const success = await copyToClipboard(code);
      if (success) {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      }
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-white">Addım Detalları</h2>
            <p className="text-sm text-gray-400 mt-1">Node ID: {nodeId}</p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-lg hover:bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
          {/* Description */}
          {description && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-white mb-3">Təsvir</h3>
              <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                <p className="text-gray-200 leading-relaxed whitespace-pre-wrap">
                  {description}
                </p>
              </div>
            </div>
          )}

          {/* Code */}
          {code && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium text-white">
                  Kod Nümunəsi
                  {language && (
                    <span className="ml-2 px-2 py-1 bg-blue-600/20 border border-blue-500/30 rounded text-sm text-blue-300">
                      {language}
                    </span>
                  )}
                </h3>
                <button
                  onClick={handleCopyCode}
                  className="flex items-center gap-2 px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-colors text-sm"
                >
                  {copySuccess ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      Kopyalandı
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4" />
                      Kopyala
                    </>
                  )}
                </button>
              </div>
              
              <div className="bg-gray-950 border border-gray-700 rounded-lg overflow-hidden">
                <pre className="p-4 overflow-x-auto text-sm">
                  <code className="text-gray-200 font-mono whitespace-pre">
                    {code}
                  </code>
                </pre>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!description && !code && (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 opacity-50">
                <svg viewBox="0 0 64 64" fill="currentColor" className="text-gray-400">
                  <path d="M32 8L8 24v24l24 16 24-16V24L32 8zm0 8l16 12v16L32 56 16 44V28l16-12z"/>
                  <circle cx="32" cy="32" r="4"/>
                </svg>
              </div>
              <p className="text-gray-400">Bu addım üçün ətraflı məlumat mövcud deyil</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Bağla
          </button>
        </div>
      </div>
    </div>
  );
};

export default DetailsModal;
