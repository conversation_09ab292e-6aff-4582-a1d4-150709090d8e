import { useState, useCallback } from "react";
import { API_CONFIG, ERROR_MESSAGES } from "../constants";
import { apiKeyUtils, retry, formatError } from "../utils";

export const useGeminiApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callGemini = useCallback(async (prompt, conversationHistory = []) => {
    const apiKey = apiKeyUtils.get();

    if (!apiKeyUtils.isValid(apiKey)) {
      throw new Error(ERROR_MESSAGES.NO_API_KEY);
    }

    setLoading(true);
    setError(null);

    try {
      const response = await retry(async () => {
        const res = await fetch(`${API_CONFIG.GEMINI_API_URL}?key=${apiKey}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contents: [
              {
                role: "user",
                parts: [{ text: prompt }],
              },
              ...conversationHistory.map((msg) => ({
                role: msg.role === "assistant" ? "model" : "user",
                parts: [{ text: msg.content || msg.text || "" }],
              })),
            ],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 8192,
            },
            safetySettings: [
              {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
            ],
          }),
        });

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          throw new Error(
            errorData.error?.message || `HTTP ${res.status}: ${res.statusText}`
          );
        }

        return res.json();
      });

      console.log("Gemini API Response:", response);

      if (!response.candidates || response.candidates.length === 0) {
        console.error("No candidates in response:", response);
        throw new Error(ERROR_MESSAGES.INVALID_RESPONSE);
      }

      const candidate = response.candidates[0];
      console.log("Candidate:", candidate);

      if (
        !candidate.content ||
        !candidate.content.parts ||
        candidate.content.parts.length === 0
      ) {
        console.error("Invalid candidate content:", candidate);
        throw new Error(ERROR_MESSAGES.INVALID_RESPONSE);
      }

      const text = candidate.content.parts[0].text;
      console.log("Extracted text:", text);

      if (!text || text.trim().length === 0) {
        console.error("Empty text response");
        throw new Error(ERROR_MESSAGES.GENERATION_FAILED);
      }

      return text.trim();
    } catch (err) {
      const errorMessage = formatError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const generateFlowchart = useCallback(
    async (description, language) => {
      const systemPrompt = `
Sən Azərbaycan dilində blok-sxem yaradıcısısan. İstifadəçinin təsvirindən ${language} dili üçün Mermaid.js formatında blok-sxem yaradırsan.

QAYDALAR:
1. YALNIZ Mermaid.js flowchart sintaksisini istifadə et
2. Hər node üçün click event əlavə et: click NODEID call showDetails("NODEID")
3. Hər node üçün %% şərh əlavə et ki, təfərrüatları və kod nümunələrini ehtiva etsin
4. Şərhlər formatı: %% NODEID:::Təsvir__NL__\`\`\`${language}__NL__kod__NL__\`\`\`
5. __NL__ yeni sətir üçün istifadə et
6. Node ID-ləri A1, B1, C1... formatında olsun
7. Başlanğıc node-u (("Başlanğıc")) formatında yaz
8. Son node-u (("Son")) formatında yaz
9. Şərti bloklar üçün diamond shape istifadə et: D1{Şərt}
10. Proseslər üçün rectangle istifadə et: B1["Proses"]
11. Input/Output üçün parallelogram istifadə et: C1[/"Input/Output"/]

NÜMUNƏ ÇIXIŞ:
graph TD;
%% A1:::Bu addım proqramın başlanğıc nöqtəsini bildirir. Heç bir kod tələb olunmur.
%% B1:::Bu addımda istifadəçidən məlumat almaq üçün lazım olan kitabxananı idxal edirik.__NL__\`\`\`${language}__NL__// Kod nümunəsi__NL__\`\`\`
A1(("Başlanğıc")) --> B1["Kitabxananı İdxal Et"];
click A1 call showDetails("A1")
click B1 call showDetails("B1")

İstifadəçi təsviri: "${description}"
`;

      return await callGemini(systemPrompt);
    },
    [callGemini]
  );

  const chatWithAI = useCallback(
    async (message, mermaidContext, details, conversationHistory = []) => {
      const chatPrompt = `
Sən Azərbaycan dilində blok-sxem və proqramlaşdırma üzrə köməkçisən.

QAYDALAR:
1. YALNIZ Azərbaycan dilində cavab ver
2. Markdown formatında cavab ver
3. Kod nümunələrini \`\`\` blokları ilə əhatə et
4. YALNIZ verilmiş blok-sxem və onunla əlaqəli proqramlaşdırma mövzuları haqqında danış
5. Kənar suallara cavab verməkdən imtina et və istifadəçini nəzakətlə mövzuya qaytar

Blok-sxem konteksti (istifadəçi görmür, yalnız sən kontekst üçün istifadə edirsən): \`\`\`mermaid
${mermaidContext}
\`\`\`

Detallar (istifadəçi görmür): ${JSON.stringify(Array.from(details.entries()))}

İstifadəçi sualı: ${message}
`;

      return await callGemini(chatPrompt, conversationHistory);
    },
    [callGemini]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    callGemini,
    generateFlowchart,
    chatWithAI,
    clearError,
  };
};
