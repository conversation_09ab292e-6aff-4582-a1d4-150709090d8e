import { useState, useRef } from 'react';
import { Play, Menu, MessageSquare, AlertCircle } from 'lucide-react';
import { PROGRAMMING_LANGUAGES, PLACEHOLDERS, ERROR_MESSAGES } from '../constants';
import FlowchartDisplay from './FlowchartDisplay';

const MainPanel = ({
  description,
  setDescription,
  language,
  setLanguage,
  mermaidCode,
  details,
  onGenerate,
  onDetailsClick,
  loading,
  error,
  onToggleSidebar,
  onToggleChat,
  disabled = false
}) => {
  const [inputHeight, setInputHeight] = useState(120);
  const textareaRef = useRef(null);

  const handleGenerate = () => {
    if (!description.trim()) {
      return;
    }
    onGenerate();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleGenerate();
    }
  };

  const handleTextareaInput = (e) => {
    const textarea = e.target;
    textarea.style.height = 'auto';
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 120), 200);
    textarea.style.height = newHeight + 'px';
    setInputHeight(newHeight);
  };

  return (
    <div className="flex-1 flex flex-col min-h-0 relative">
      {/* Mobile Header */}
      <div className="md:hidden flex items-center justify-between p-4 bg-gray-900 border-b border-gray-700">
        <button
          onClick={onToggleSidebar}
          className="w-10 h-10 rounded-lg bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-300 transition-colors"
        >
          <Menu className="w-5 h-5" />
        </button>
        <h1 className="text-lg font-semibold text-white">AI Blok-sxem</h1>
        <button
          onClick={onToggleChat}
          disabled={!mermaidCode}
          className="w-10 h-10 rounded-lg bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-gray-300 transition-colors"
        >
          <MessageSquare className="w-5 h-5" />
        </button>
      </div>

      {/* Desktop Toggle Controls */}
      <div className="hidden md:flex absolute top-4 left-4 z-30 gap-2">
        <button
          onClick={onToggleSidebar}
          title="Sidebar-ı aç/bağla"
          className="w-10 h-10 bg-gray-800/90 backdrop-blur-sm border border-gray-700 rounded-lg hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
        >
          <Menu className="w-4 h-4" />
        </button>
        <button
          onClick={onToggleChat}
          disabled={!mermaidCode}
          title="Chat panelini aç/bağla"
          className="w-10 h-10 bg-gray-800/90 backdrop-blur-sm border border-gray-700 rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-gray-300 hover:text-white transition-colors"
        >
          <MessageSquare className="w-4 h-4" />
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* Flowchart Display */}
        <div className="flex-1 relative">
          <FlowchartDisplay
            mermaidCode={mermaidCode}
            onDetailsClick={onDetailsClick}
            className="absolute inset-0"
          />
        </div>

        {/* Input Panel */}
        <div className="bg-gray-900 border-t border-gray-700 p-4">
          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            </div>
          )}

          <div className="max-w-4xl mx-auto">
            {/* Language Selector */}
            <div className="mb-3">
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                disabled={loading || disabled}
                className="w-full md:w-auto px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none disabled:opacity-50"
              >
                {PROGRAMMING_LANGUAGES.map((lang) => (
                  <option key={lang.value} value={lang.value}>
                    {lang.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Input Area */}
            <div className="flex gap-3">
              <div className="flex-1">
                <textarea
                  ref={textareaRef}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  onInput={handleTextareaInput}
                  onKeyPress={handleKeyPress}
                  placeholder={PLACEHOLDERS.INPUT}
                  disabled={loading || disabled}
                  rows={3}
                  className="w-full resize-none bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none disabled:opacity-50 transition-all"
                  style={{ 
                    minHeight: '120px',
                    maxHeight: '200px',
                    height: `${inputHeight}px`
                  }}
                />
                
                {/* Keyboard Shortcut Hint */}
                <p className="text-xs text-gray-500 mt-2">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs">Ctrl</kbd> + 
                  <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs ml-1">Enter</kbd> 
                  <span className="ml-1">ilə göndər</span>
                </p>
              </div>

              <div className="flex flex-col justify-end">
                <button
                  onClick={handleGenerate}
                  disabled={!description.trim() || loading || disabled}
                  className="w-12 h-12 md:w-auto md:h-auto md:px-6 md:py-3 bg-blue-600 hover:bg-blue-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center gap-2 transition-colors font-medium"
                >
                  {loading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <Play className="w-5 h-5" />
                      <span className="hidden md:inline">Yarat</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Status Text */}
            {loading && (
              <div className="mt-3 flex items-center justify-center gap-2 text-blue-400">
                <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Blok-sxem yaradılır...</span>
              </div>
            )}

            {disabled && (
              <div className="mt-3 text-center">
                <p className="text-sm text-gray-500">
                  API açarı tələb olunur. Tənzimləmələrdən API açarınızı daxil edin.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainPanel;
