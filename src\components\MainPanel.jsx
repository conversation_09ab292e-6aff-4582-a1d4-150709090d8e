import { useState, useRef } from "react";
import { Play, Menu, MessageSquare, AlertCircle } from "lucide-react";
import {
  PROGRAMMING_LANGUAGES,
  PLACEHOLDERS,
  ERROR_MESSAGES,
} from "../constants";
import FlowchartDisplay from "./FlowchartDisplay";

const MainPanel = ({
  description,
  setDescription,
  language,
  setLanguage,
  mermaidCode,
  details,
  onGenerate,
  onDetailsClick,
  loading,
  error,
  onToggleSidebar,
  onToggleChat,
  disabled = false,
  sidebarOpen,
  chatOpen,
}) => {
  const [inputHeight, setInputHeight] = useState(120);
  const textareaRef = useRef(null);

  const handleGenerate = () => {
    if (!description.trim()) {
      return;
    }
    onGenerate();
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleGenerate();
    }
  };

  const handleTextareaInput = (e) => {
    const textarea = e.target;
    textarea.style.height = "auto";
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 120), 200);
    textarea.style.height = newHeight + "px";
    setInputHeight(newHeight);
  };

  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Canvas - Full Screen Background */}
      <FlowchartDisplay
        mermaidCode={mermaidCode}
        onDetailsClick={onDetailsClick}
        className="absolute inset-0 w-full h-full"
      />

      {/* Floating Controls - Top Left */}
      <div className="absolute top-4 left-4 z-30 flex gap-2">
        <button
          onClick={onToggleSidebar}
          title="Sidebar-ı aç/bağla"
          className="w-10 h-10 rounded-lg hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
          style={{
            backgroundColor: "var(--surface-1)",
            border: "1px solid var(--border-color)",
            backdropFilter: "blur(10px)",
          }}
        >
          <Menu className="w-4 h-4" />
        </button>
        <button
          onClick={onToggleChat}
          disabled={!mermaidCode}
          title="Chat panelini aç/bağla"
          className="w-10 h-10 rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-gray-300 hover:text-white transition-colors"
          style={{
            backgroundColor: "var(--surface-1)",
            border: "1px solid var(--border-color)",
            backdropFilter: "blur(10px)",
          }}
        >
          <MessageSquare className="w-4 h-4" />
        </button>
      </div>

      {/* Floating Input Panel - Bottom */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 w-full max-w-4xl px-4">
        <div
          className="rounded-lg p-4 shadow-xl"
          style={{
            backgroundColor: "var(--surface-1)",
            border: "1px solid var(--border-color)",
            backdropFilter: "blur(10px)",
          }}
        >
          {/* Error Display */}
          {error && (
            <div
              className="mb-4 p-3 rounded-lg flex items-start gap-2"
              style={{
                backgroundColor: "rgba(239, 68, 68, 0.1)",
                border: "1px solid rgba(239, 68, 68, 0.3)",
              }}
            >
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Language Selector */}
          <div className="mb-3">
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              disabled={loading || disabled}
              className="w-full md:w-auto px-3 py-2 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none disabled:opacity-50"
              style={{
                backgroundColor: "var(--surface-2)",
                border: "1px solid var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              {PROGRAMMING_LANGUAGES.map((lang) => (
                <option key={lang.value} value={lang.value}>
                  {lang.label}
                </option>
              ))}
            </select>
          </div>

          {/* Input Area */}
          <div className="flex gap-3">
            <div className="flex-1">
              <textarea
                ref={textareaRef}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onInput={handleTextareaInput}
                onKeyPress={handleKeyPress}
                placeholder={PLACEHOLDERS.INPUT}
                disabled={loading || disabled}
                rows={3}
                className="w-full resize-none rounded-lg px-4 py-3 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none disabled:opacity-50 transition-all"
                style={{
                  minHeight: "120px",
                  maxHeight: "200px",
                  height: `${inputHeight}px`,
                  backgroundColor: "var(--surface-2)",
                  border: "1px solid var(--border-color)",
                  color: "var(--text-primary)",
                }}
              />

              {/* Keyboard Shortcut Hint */}
              <p
                className="text-xs mt-2"
                style={{ color: "var(--text-secondary)" }}
              >
                <kbd
                  className="px-1.5 py-0.5 rounded text-xs"
                  style={{ backgroundColor: "var(--surface-3)" }}
                >
                  Ctrl
                </kbd>{" "}
                +
                <kbd
                  className="px-1.5 py-0.5 rounded text-xs ml-1"
                  style={{ backgroundColor: "var(--surface-3)" }}
                >
                  Enter
                </kbd>
                <span className="ml-1">ilə göndər</span>
              </p>
            </div>

            <div className="flex flex-col justify-end">
              <button
                onClick={handleGenerate}
                disabled={!description.trim() || loading || disabled}
                className="w-12 h-12 md:w-auto md:h-auto md:px-6 md:py-3 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center gap-2 transition-all font-medium"
                style={{
                  backgroundColor: "var(--accent-color)",
                  color: "var(--accent-text)",
                }}
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    <span className="hidden md:inline">Yarat</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Status Text */}
          {loading && (
            <div
              className="mt-3 flex items-center justify-center gap-2"
              style={{ color: "var(--accent-color)" }}
            >
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">Blok-sxem yaradılır...</span>
            </div>
          )}

          {disabled && (
            <div className="mt-3 text-center">
              <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                API açarı tələb olunur. Tənzimləmələrdən API açarınızı daxil
                edin.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainPanel;
