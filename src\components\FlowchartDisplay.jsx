import { useEffect, useRef, useState } from "react";
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize2,
  Copy,
  Download,
} from "lucide-react";
import { useMermaid } from "../hooks/useMermaid";
import { copyToClipboard } from "../utils";

const FlowchartDisplay = ({
  mermaidCode,
  onDetailsClick,
  className = "",
  showControls = true,
}) => {
  const containerRef = useRef(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showDetails, setShowDetails] = useState(null);

  const {
    loading,
    error,
    renderDiagram,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToScreen,
    clearDiagram,
    clearError,
  } = useMermaid();

  // Diaqramı render et
  useEffect(() => {
    if (mermaidCode && containerRef.current) {
      renderDiagram(mermaidCode, containerRef.current);
    } else if (containerRef.current) {
      clearDiagram(containerRef.current);
    }
  }, [mermaidCode, renderDiagram, clearDiagram]);

  // Global showDetails funksiyasını təyin et (Mermaid click events üçün)
  useEffect(() => {
    console.log("Setting up window.showDetails function");
    window.showDetails = (nodeId) => {
      console.log("showDetails called with nodeId:", nodeId);
      if (onDetailsClick) {
        console.log("Calling onDetailsClick with:", nodeId);
        onDetailsClick(nodeId);
      } else {
        console.log("onDetailsClick not available");
      }
      setShowDetails(nodeId);
    };

    return () => {
      console.log("Cleaning up window.showDetails");
      delete window.showDetails;
    };
  }, [onDetailsClick]);

  // Kodu kopyala
  const handleCopyCode = async () => {
    if (mermaidCode) {
      const success = await copyToClipboard(mermaidCode);
      if (success) {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      }
    }
  };

  // SVG-ni yüklə
  const handleDownloadSVG = () => {
    const svgElement = containerRef.current?.querySelector("svg");
    if (svgElement) {
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], {
        type: "image/svg+xml;charset=utf-8",
      });
      const svgUrl = URL.createObjectURL(svgBlob);

      const downloadLink = document.createElement("a");
      downloadLink.href = svgUrl;
      downloadLink.download = "flowchart.svg";
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);
    }
  };

  return (
    <div className={`relative w-full h-full bg-gray-950 ${className}`}>
      {/* Loading State */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-950/80 backdrop-blur-sm z-10">
          <div className="flex items-center gap-3 text-white">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>Diaqram yaradılır...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center p-4 z-10">
          <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 max-w-md text-center">
            <p className="text-red-300 mb-3">{error}</p>
            <button
              onClick={clearError}
              className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-colors"
            >
              Yenidən cəhd et
            </button>
          </div>
        </div>
      )}

      {/* Controls */}
      {showControls && mermaidCode && !loading && !error && (
        <div className="absolute top-4 right-4 flex gap-2 z-20">
          <div className="bg-gray-800/90 backdrop-blur-sm border border-gray-700 rounded-lg p-1 flex gap-1">
            <button
              onClick={zoomIn}
              title="Böyüt"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
            <button
              onClick={zoomOut}
              title="Kiçilt"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <button
              onClick={resetZoom}
              title="Sıfırla"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={fitToScreen}
              title="Ekrana sığdır"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>

          <div className="bg-gray-800/90 backdrop-blur-sm border border-gray-700 rounded-lg p-1 flex gap-1">
            <button
              onClick={handleCopyCode}
              title="Kodu kopyala"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={handleDownloadSVG}
              title="SVG yüklə"
              className="w-8 h-8 rounded hover:bg-gray-700 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Copy Success Notification */}
      {copySuccess && (
        <div className="absolute top-16 right-4 bg-green-600 text-white px-3 py-2 rounded-lg text-sm z-30 animate-fade-in-out">
          Kod kopyalandı!
        </div>
      )}

      {/* Main Container */}
      <div
        ref={containerRef}
        id="flowchart-container"
        className="w-full h-full flex items-center justify-center overflow-hidden"
        style={{ cursor: mermaidCode ? "grab" : "default" }}
      >
        {!mermaidCode && !loading && !error && (
          <div className="text-center text-gray-400">
            <div className="w-16 h-16 mx-auto mb-4 opacity-50">
              <svg viewBox="0 0 64 64" fill="currentColor">
                <path d="M32 8L8 24v24l24 16 24-16V24L32 8zm0 8l16 12v16L32 56 16 44V28l16-12z" />
                <circle cx="32" cy="20" r="3" />
                <circle cx="20" cy="32" r="3" />
                <circle cx="44" cy="32" r="3" />
                <circle cx="32" cy="44" r="3" />
              </svg>
            </div>
            <p className="text-lg font-medium mb-2">
              Blok-sxeminiz burada göstəriləcək
            </p>
            <p className="text-sm">
              Alqoritminizi təsvir edin və "Yarat" düyməsini basın
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FlowchartDisplay;
