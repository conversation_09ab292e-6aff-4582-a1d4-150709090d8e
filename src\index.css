@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --background: #000000;
  --surface-1: #111111;
  --surface-2: #1f1f1f;
  --surface-3: #2c2c2c;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --border-color: #3a3a3a;
  --accent-color: #ffffff;
  --accent-text: #000000;

  /* Panel dimensions */
  --panel-width-collapsed: 0px;
  --panel-width-expanded: 280px;
  --chat-width-collapsed: 0px;
  --chat-width-expanded: 320px;
  --input-panel-height: 200px;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

html {
  height: 100%;
  overflow: hidden;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--surface-1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--surface-3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

::-webkit-scrollbar-corner {
  background: var(--surface-1);
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.animate-fade-in-out {
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  20% {
    opacity: 1;
    transform: translateY(0);
  }
  80% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Prose Styles for Markdown */
.prose {
  color: inherit;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose code {
  background-color: rgba(55, 65, 81, 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(55, 65, 81, 0.5);
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

.prose ul,
.prose ol {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
  padding-left: 1.5em;
}

.prose li {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

.prose blockquote {
  border-left: 4px solid rgba(59, 130, 246, 0.5);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: rgba(156, 163, 175, 1);
}

.prose a {
  color: rgb(59, 130, 246);
  text-decoration: underline;
}

.prose a:hover {
  color: rgb(96, 165, 250);
}

/* Mermaid Diagram Styles */
.mermaid {
  background: transparent !important;
}

/* Focus Styles */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid rgb(59, 130, 246);
  outline-offset: 2px;
}

/* Disabled States */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .md\:hidden {
    display: none !important;
  }

  .md\:flex {
    display: flex !important;
  }

  .md\:relative {
    position: relative !important;
  }
}
