import { useState, useEffect } from 'react';
import { X, Key, ExternalLink, AlertCircle, CheckCircle } from 'lucide-react';
import { apiKeyUtils } from '../utils';

const ApiKeyModal = ({ isOpen, onClose, onSave }) => {
  const [apiKey, setApiKey] = useState('');
  const [status, setStatus] = useState('idle'); // idle, testing, success, error
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (isOpen) {
      const savedKey = apiKeyUtils.get();
      if (savedKey) {
        setApiKey(savedKey);
        setStatus('success');
        setMessage('API açarı saxlanılıb');
      } else {
        setApiKey('');
        setStatus('idle');
        setMessage('');
      }
    }
  }, [isOpen]);

  const handleSave = async () => {
    const trimmedKey = apiKey.trim();
    
    if (!trimmedKey) {
      setStatus('error');
      setMessage('API açarı boş ola bilməz');
      return;
    }

    if (trimmedKey.length < 10) {
      setStatus('error');
      setMessage('API açarı çox qısadır');
      return;
    }

    setStatus('testing');
    setMessage('API açarı yoxlanılır...');

    try {
      // API açarını test et
      const testResponse = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${trimmedKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              role: 'user',
              parts: [{ text: 'Test' }]
            }],
            generationConfig: {
              maxOutputTokens: 10
            }
          })
        }
      );

      if (testResponse.ok || testResponse.status === 400) {
        // 400 da qəbul edilir çünki test mesajı çox qısadır
        apiKeyUtils.set(trimmedKey);
        setStatus('success');
        setMessage('API açarı uğurla saxlanıldı');
        
        if (onSave) {
          onSave(trimmedKey);
        }
        
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        throw new Error(`HTTP ${testResponse.status}`);
      }
    } catch (error) {
      console.error('API key test error:', error);
      setStatus('error');
      setMessage('API açarı yanlışdır və ya şəbəkə xətası');
    }
  };

  const handleClose = () => {
    if (status !== 'testing') {
      onClose();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && status !== 'testing') {
      handleSave();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-md w-full p-6 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Key className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white">Google Gemini API</h2>
              <p className="text-sm text-gray-400">API açarınızı daxil edin</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={status === 'testing'}
            className="w-8 h-8 rounded-lg hover:bg-gray-800 flex items-center justify-center transition-colors disabled:opacity-50"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>

        {/* API Key Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            API Açarı
          </label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="AIzaSy..."
            disabled={status === 'testing'}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-white placeholder-gray-500 disabled:opacity-50"
          />
        </div>

        {/* Status Message */}
        {message && (
          <div className={`mb-4 p-3 rounded-lg flex items-center gap-2 text-sm ${
            status === 'success' 
              ? 'bg-green-900/50 border border-green-700 text-green-300'
              : status === 'error'
              ? 'bg-red-900/50 border border-red-700 text-red-300'
              : 'bg-blue-900/50 border border-blue-700 text-blue-300'
          }`}>
            {status === 'success' && <CheckCircle className="w-4 h-4" />}
            {status === 'error' && <AlertCircle className="w-4 h-4" />}
            {status === 'testing' && (
              <div className="w-4 h-4 border-2 border-blue-300 border-t-transparent rounded-full animate-spin" />
            )}
            <span>{message}</span>
          </div>
        )}

        {/* Instructions */}
        <div className="mb-6 p-3 bg-gray-800/50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-300 mb-2">API açarı necə əldə edilir:</h3>
          <ol className="text-xs text-gray-400 space-y-1">
            <li>1. Google AI Studio saytına daxil olun</li>
            <li>2. "Get API Key" düyməsini basın</li>
            <li>3. Yeni API açarı yaradın</li>
            <li>4. API açarını kopyalayıb buraya yapışdırın</li>
          </ol>
          <a
            href="https://makersuite.google.com/app/apikey"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 mt-2 transition-colors"
          >
            Google AI Studio-ya get
            <ExternalLink className="w-3 h-3" />
          </a>
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <button
            onClick={handleClose}
            disabled={status === 'testing'}
            className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            Ləğv et
          </button>
          <button
            onClick={handleSave}
            disabled={status === 'testing' || !apiKey.trim()}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
          >
            {status === 'testing' ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Yoxlanılır...
              </>
            ) : (
              'Saxla'
            )}
          </button>
        </div>

        {/* Security Note */}
        <p className="text-xs text-gray-500 mt-4 text-center">
          API açarınız yalnız brauzerdə saxlanılır və heç yerə göndərilmir
        </p>
      </div>
    </div>
  );
};

export default ApiKeyModal;
