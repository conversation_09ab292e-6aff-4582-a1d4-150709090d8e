import { useState, useEffect, useCallback } from "react";
import { useSessionManager } from "./hooks/useSessionManager";
import { useGeminiApi } from "./hooks/useGeminiApi";
import {
  apiKeyUtils,
  extractDetailsFromMermaid,
  cleanMermaidCode,
} from "./utils";
import { ERROR_MESSAGES } from "./constants";

// Components
import Sidebar from "./components/Sidebar";
import MainPanel from "./components/MainPanel";
import ChatPanel from "./components/ChatPanel";
import ApiKeyModal from "./components/ApiKeyModal";
import DetailsModal from "./components/DetailsModal";

function App() {
  // UI State
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [apiModalOpen, setApiModalOpen] = useState(false);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState(null);

  // Form State
  const [description, setDescription] = useState("");
  const [language, setLanguage] = useState("javascript");

  // Session Management
  const {
    sessions,
    activeSessionId,
    activeSession,
    createNewSession,
    loadSession,
    updateSessionDescription,
    updateSessionLanguage,
    updateSessionMermaidCode,
    addChatMessage,
    clearConversationHistory,
  } = useSessionManager();

  // API
  const {
    loading: apiLoading,
    error: apiError,
    generateFlowchart,
    chatWithAI,
    clearError: clearApiError,
  } = useGeminiApi();

  // API Key durumu
  const [hasApiKey, setHasApiKey] = useState(false);

  // API key-i yoxla
  useEffect(() => {
    const checkApiKey = () => {
      const apiKey = apiKeyUtils.get();
      setHasApiKey(apiKeyUtils.isValid(apiKey));
    };

    checkApiKey();

    // Storage dəyişikliklərini dinlə
    const handleStorageChange = () => {
      checkApiKey();
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  // Aktiv sessiya dəyişdikdə form state-ni yenilə
  useEffect(() => {
    if (activeSession) {
      setDescription(activeSession.description || "");
      setLanguage(activeSession.language || "javascript");
    }
  }, [activeSession]);

  // API key yoxdursa modal aç
  useEffect(() => {
    if (!hasApiKey && sessions.length > 0) {
      setApiModalOpen(true);
    }
  }, [hasApiKey, sessions.length]);

  // Blok-sxem yaratma
  const handleGenerate = useCallback(async () => {
    if (!description.trim()) {
      return;
    }

    if (!hasApiKey) {
      setApiModalOpen(true);
      return;
    }

    try {
      clearApiError();

      // Aktiv sessiyanu yenilə
      if (activeSessionId) {
        updateSessionDescription(activeSessionId, description);
        updateSessionLanguage(activeSessionId, language);
      }

      // Blok-sxem yarat
      const mermaidCode = await generateFlowchart(description, language);
      const cleanCode = cleanMermaidCode(mermaidCode);
      const details = extractDetailsFromMermaid(cleanCode);

      // Sessiyanu yenilə
      if (activeSessionId) {
        updateSessionMermaidCode(activeSessionId, cleanCode, details);
      }
    } catch (error) {
      console.error("Generate flowchart error:", error);
    }
  }, [
    description,
    language,
    hasApiKey,
    activeSessionId,
    generateFlowchart,
    updateSessionDescription,
    updateSessionLanguage,
    updateSessionMermaidCode,
    clearApiError,
  ]);

  // Chat mesajı göndər
  const handleSendChatMessage = useCallback(
    async (message) => {
      if (!activeSession || !activeSession.mermaidCode) {
        return;
      }

      try {
        // İstifadəçi mesajını əlavə et
        const userMessage = { role: "user", content: message };
        addChatMessage(activeSessionId, userMessage);

        // AI cavabını al
        const aiResponse = await chatWithAI(
          message,
          activeSession.mermaidCode,
          activeSession.details || new Map(),
          activeSession.conversationHistory || []
        );

        // AI mesajını əlavə et
        const aiMessage = { role: "assistant", content: aiResponse };
        addChatMessage(activeSessionId, aiMessage);
      } catch (error) {
        console.error("Chat error:", error);
        const errorMessage = {
          role: "assistant",
          content:
            "Üzr istəyirəm, cavab verərkən xəta baş verdi. Zəhmət olmasa yenidən cəhd edin.",
        };
        addChatMessage(activeSessionId, errorMessage);
      }
    },
    [activeSession, activeSessionId, chatWithAI, addChatMessage]
  );

  // Sessiya seç
  const handleSessionSelect = useCallback(
    (sessionId) => {
      loadSession(sessionId);
      setSidebarOpen(false); // Mobile-da sidebar-ı bağla
    },
    [loadSession]
  );

  // Yeni sessiya yarat
  const handleNewSession = useCallback(() => {
    createNewSession();
    setSidebarOpen(false); // Mobile-da sidebar-ı bağla
  }, [createNewSession]);

  // Node detallarını göstər
  const handleDetailsClick = useCallback((nodeId) => {
    setSelectedNodeId(nodeId);
    setDetailsModalOpen(true);
  }, []);

  // API key saxla
  const handleApiKeySave = useCallback((apiKey) => {
    setHasApiKey(true);
    setApiModalOpen(false);
  }, []);

  // Chat tarixçəsini təmizlə
  const handleClearChatHistory = useCallback(() => {
    if (activeSessionId) {
      clearConversationHistory(activeSessionId);
    }
  }, [activeSessionId, clearConversationHistory]);

  // İlk sessiya yarat (əgər yoxdursa)
  useEffect(() => {
    if (sessions.length === 0) {
      createNewSession();
    }
  }, [sessions.length, createNewSession]);

  return (
    <div className="h-screen bg-gray-950 text-white overflow-hidden flex">
      {/* Sidebar */}
      <Sidebar
        sessions={sessions}
        activeSessionId={activeSessionId}
        onSessionSelect={handleSessionSelect}
        onNewSession={handleNewSession}
        onDeleteSession={(sessionId) => {
          // TODO: Implement delete session
        }}
        onRenameSession={(sessionId, newName) => {
          // TODO: Implement rename session
        }}
        onOpenApiModal={() => setApiModalOpen(true)}
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Main Panel */}
      <MainPanel
        description={description}
        setDescription={setDescription}
        language={language}
        setLanguage={setLanguage}
        mermaidCode={activeSession?.mermaidCode || ""}
        details={activeSession?.details || new Map()}
        onGenerate={handleGenerate}
        onDetailsClick={handleDetailsClick}
        loading={apiLoading}
        error={apiError}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        onToggleChat={() => setChatOpen(!chatOpen)}
        disabled={!hasApiKey}
      />

      {/* Chat Panel */}
      <ChatPanel
        isOpen={chatOpen}
        onClose={() => setChatOpen(false)}
        messages={activeSession?.conversationHistory || []}
        onSendMessage={handleSendChatMessage}
        onClearHistory={handleClearChatHistory}
        loading={apiLoading}
        disabled={!activeSession?.mermaidCode}
      />

      {/* Modals */}
      <ApiKeyModal
        isOpen={apiModalOpen}
        onClose={() => setApiModalOpen(false)}
        onSave={handleApiKeySave}
      />

      <DetailsModal
        isOpen={detailsModalOpen}
        onClose={() => setDetailsModalOpen(false)}
        nodeId={selectedNodeId}
        details={activeSession?.details || new Map()}
      />
    </div>
  );
}

export default App;
