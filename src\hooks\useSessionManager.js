import { useState, useCallback, useEffect } from "react";
import { STORAGE_KEYS, PLACEHOLDERS } from "../constants";
import { storage, generateSessionId } from "../utils";

export const useSessionManager = () => {
  const [sessions, setSessions] = useState([]);
  const [activeSessionId, setActiveSessionId] = useState(null);
  const [activeSession, setActiveSession] = useState(null);

  // Sessiyaları yüklə
  const loadSessions = useCallback(() => {
    const savedSessions = storage.get(STORAGE_KEYS.SESSIONS) || [];
    const savedActiveId = storage.get(STORAGE_KEYS.ACTIVE_SESSION);

    // Yüklənən sessiyaları düzgün format et
    const formattedSessions = savedSessions.map((session) => ({
      ...session,
      details: session.details
        ? new Map(Object.entries(session.details))
        : new Map(),
      conversationHistory: session.conversationHistory || [],
    }));

    setSessions(formattedSessions);

    if (
      savedActiveId &&
      formattedSessions.find((s) => s.id === savedActiveId)
    ) {
      setActiveSessionId(savedActiveId);
    } else if (formattedSessions.length > 0) {
      setActiveSessionId(formattedSessions[0].id);
    }
  }, []);

  // Sessiyaları saxla
  const saveSessions = useCallback((newSessions) => {
    // Map obyektlərini serialize etmək üçün obyektə çevir
    const serializedSessions = newSessions.map((session) => ({
      ...session,
      details:
        session.details instanceof Map
          ? Object.fromEntries(session.details)
          : session.details || {},
    }));

    storage.set(STORAGE_KEYS.SESSIONS, serializedSessions);
    setSessions(newSessions);
  }, []);

  // Aktiv sessiya ID-ni saxla
  const saveActiveSessionId = useCallback((sessionId) => {
    storage.set(STORAGE_KEYS.ACTIVE_SESSION, sessionId);
    setActiveSessionId(sessionId);
  }, []);

  // Yeni sessiya yarat
  const createNewSession = useCallback(() => {
    const newSession = {
      id: generateSessionId(),
      name: PLACEHOLDERS.SESSION_NAME,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      description: "",
      language: "javascript",
      mermaidCode: "",
      details: new Map(),
      conversationHistory: [],
    };

    const newSessions = [newSession, ...sessions];
    saveSessions(newSessions);
    saveActiveSessionId(newSession.id);

    return newSession;
  }, [sessions, saveSessions, saveActiveSessionId]);

  // Sessiya yüklə
  const loadSession = useCallback(
    (sessionId) => {
      const session = sessions.find((s) => s.id === sessionId);
      if (session) {
        // Map obyektini yenidən yarat (JSON-dan gələndə Map olmur)
        if (session.details && !(session.details instanceof Map)) {
          session.details = new Map(Object.entries(session.details));
        }
        if (!session.details) {
          session.details = new Map();
        }

        setActiveSession(session);
        saveActiveSessionId(sessionId);
        return session;
      }
      return null;
    },
    [sessions, saveActiveSessionId]
  );

  // Sessiya yenilə
  const updateSession = useCallback(
    (sessionId, updates) => {
      const newSessions = sessions.map((session) => {
        if (session.id === sessionId) {
          const updatedSession = {
            ...session,
            ...updates,
            updatedAt: Date.now(),
          };

          // Aktiv sessiya da yenilənsin
          if (sessionId === activeSessionId) {
            setActiveSession(updatedSession);
          }

          return updatedSession;
        }
        return session;
      });

      saveSessions(newSessions);
    },
    [sessions, activeSessionId, saveSessions]
  );

  // Sessiya sil
  const deleteSession = useCallback(
    (sessionId) => {
      const newSessions = sessions.filter((s) => s.id !== sessionId);
      saveSessions(newSessions);

      if (sessionId === activeSessionId) {
        if (newSessions.length > 0) {
          loadSession(newSessions[0].id);
        } else {
          setActiveSessionId(null);
          setActiveSession(null);
        }
      }
    },
    [sessions, activeSessionId, saveSessions, loadSession]
  );

  // Sessiya adını dəyiş
  const renameSession = useCallback(
    (sessionId, newName) => {
      updateSession(sessionId, {
        name: newName.trim() || PLACEHOLDERS.SESSION_NAME,
      });
    },
    [updateSession]
  );

  // Sessiya təsvirini yenilə
  const updateSessionDescription = useCallback(
    (sessionId, description) => {
      updateSession(sessionId, { description });
    },
    [updateSession]
  );

  // Sessiya dilini yenilə
  const updateSessionLanguage = useCallback(
    (sessionId, language) => {
      updateSession(sessionId, { language });
    },
    [updateSession]
  );

  // Mermaid kodunu yenilə
  const updateSessionMermaidCode = useCallback(
    (sessionId, mermaidCode, details) => {
      updateSession(sessionId, {
        mermaidCode,
        details:
          details instanceof Map
            ? details
            : new Map(Object.entries(details || {})),
      });
    },
    [updateSession]
  );

  // Söhbət tarixçəsini yenilə
  const updateConversationHistory = useCallback(
    (sessionId, history) => {
      updateSession(sessionId, { conversationHistory: history });
    },
    [updateSession]
  );

  // Söhbət mesajı əlavə et
  const addChatMessage = useCallback(
    (sessionId, message) => {
      const session = sessions.find((s) => s.id === sessionId);
      if (session) {
        const newHistory = [...(session.conversationHistory || []), message];

        // Sessiyaları yenilə
        const newSessions = sessions.map((s) =>
          s.id === sessionId
            ? { ...s, conversationHistory: newHistory, updatedAt: Date.now() }
            : s
        );

        // Dərhal localStorage-ə saxla
        saveSessions(newSessions);

        // Aktiv sessiya da dərhal yenilənsin
        if (sessionId === activeSessionId) {
          setActiveSession((prev) => ({
            ...prev,
            conversationHistory: newHistory,
          }));
        }

        return newHistory; // Yenilənmiş tarixçəni qaytar
      }
      return null; // Sessiya tapılmadı
    },
    [sessions, activeSessionId, saveSessions]
  );

  // Söhbət tarixçəsini təmizlə
  const clearConversationHistory = useCallback(
    (sessionId) => {
      updateConversationHistory(sessionId, []);
    },
    [updateConversationHistory]
  );

  // Bütün sessiyaları sil
  const clearAllSessions = useCallback(() => {
    storage.remove(STORAGE_KEYS.SESSIONS);
    storage.remove(STORAGE_KEYS.ACTIVE_SESSION);
    setSessions([]);
    setActiveSessionId(null);
    setActiveSession(null);
  }, []);

  // Sessiya statistikaları
  const getSessionStats = useCallback(
    (sessionId) => {
      const session = sessions.find((s) => s.id === sessionId);
      if (!session) return null;

      return {
        messageCount: session.conversationHistory?.length || 0,
        hasFlowchart: Boolean(session.mermaidCode),
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        language: session.language,
      };
    },
    [sessions]
  );

  // İlk yükləmə
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  // Aktiv sessiyanu yüklə
  useEffect(() => {
    if (activeSessionId && sessions.length > 0) {
      loadSession(activeSessionId);
    }
  }, [activeSessionId, sessions, loadSession]);

  return {
    sessions,
    activeSessionId,
    activeSession,
    createNewSession,
    loadSession,
    updateSession,
    deleteSession,
    renameSession,
    updateSessionDescription,
    updateSessionLanguage,
    updateSessionMermaidCode,
    updateConversationHistory,
    addChatMessage,
    clearConversationHistory,
    clearAllSessions,
    getSessionStats,
    loadSessions,
  };
};
