import { useState, useCallback, useEffect } from "react";
import { STORAGE_KEYS, PLACEHOLDERS } from "../constants";
import { storage, generateSessionId } from "../utils";

export const useSessionManager = () => {
  const [sessions, setSessions] = useState([]);
  const [activeSessionId, setActiveSessionId] = useState(null);
  const [activeSession, setActiveSession] = useState(null);

  // Sessiyaları yüklə
  const loadSessions = () => {
    console.log("Loading sessions from localStorage...");
    const savedSessions = storage.get(STORAGE_KEYS.SESSIONS) || [];
    const savedActiveId = storage.get(STORAGE_KEYS.ACTIVE_SESSION);

    console.log("Saved sessions:", savedSessions);
    console.log("Saved active ID:", savedActiveId);

    // Yüklənən sessiyaları düzgün format et
    const formattedSessions = savedSessions.map((session) => ({
      ...session,
      details: session.details
        ? new Map(Object.entries(session.details))
        : new Map(),
      conversationHistory: session.conversationHistory || [],
    }));

    console.log("Formatted sessions:", formattedSessions);
    setSessions(formattedSessions);

    // ActiveSessionId-ni set et
    if (
      savedActiveId &&
      formattedSessions.find((s) => s.id === savedActiveId)
    ) {
      console.log("Setting active session ID:", savedActiveId);
      setActiveSessionId(savedActiveId);
    } else if (formattedSessions.length > 0) {
      console.log("Setting first session as active:", formattedSessions[0].id);
      setActiveSessionId(formattedSessions[0].id);
    } else {
      console.log("No sessions found");
      setActiveSessionId(null);
      setActiveSession(null);
    }
  };

  // Sessiyaları saxla
  const saveSessions = useCallback((newSessions) => {
    // Map obyektlərini serialize etmək üçün obyektə çevir
    const serializedSessions = newSessions.map((session) => ({
      ...session,
      details:
        session.details instanceof Map
          ? Object.fromEntries(session.details)
          : session.details || {},
    }));

    storage.set(STORAGE_KEYS.SESSIONS, serializedSessions);
    setSessions(newSessions);
  }, []);

  // Aktiv sessiya ID-ni saxla
  const saveActiveSessionId = useCallback((sessionId) => {
    storage.set(STORAGE_KEYS.ACTIVE_SESSION, sessionId);
    setActiveSessionId(sessionId);
  }, []);

  // Yeni sessiya yarat
  const createNewSession = useCallback(() => {
    const newSession = {
      id: generateSessionId(),
      name: PLACEHOLDERS.SESSION_NAME,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      description: "",
      language: "javascript",
      mermaidCode: "",
      details: new Map(),
      conversationHistory: [],
    };

    const newSessions = [newSession, ...sessions];
    saveSessions(newSessions);
    saveActiveSessionId(newSession.id);

    return newSession;
  }, [sessions, saveSessions, saveActiveSessionId]);

  // Sessiya yüklə
  const loadSession = useCallback(
    (sessionId) => {
      console.log("Loading session:", sessionId);
      const session = sessions.find((s) => s.id === sessionId);
      console.log("Found session:", session);

      if (session) {
        // Map obyektini yenidən yarat (JSON-dan gələndə Map olmur)
        if (session.details && !(session.details instanceof Map)) {
          session.details = new Map(Object.entries(session.details));
        }
        if (!session.details) {
          session.details = new Map();
        }

        console.log("Setting active session:", session);
        setActiveSession(session);
        saveActiveSessionId(sessionId);
        return session;
      }
      console.log("Session not found:", sessionId);
      return null;
    },
    [sessions, saveActiveSessionId]
  );

  // Sessiya yenilə
  const updateSession = useCallback(
    (sessionId, updates) => {
      const newSessions = sessions.map((session) => {
        if (session.id === sessionId) {
          const updatedSession = {
            ...session,
            ...updates,
            updatedAt: Date.now(),
          };

          // Aktiv sessiya da yenilənsin
          if (sessionId === activeSessionId) {
            setActiveSession(updatedSession);
          }

          return updatedSession;
        }
        return session;
      });

      saveSessions(newSessions);
    },
    [sessions, activeSessionId, saveSessions]
  );

  // Sessiya sil
  const deleteSession = useCallback(
    (sessionId) => {
      const newSessions = sessions.filter((s) => s.id !== sessionId);
      saveSessions(newSessions);

      if (sessionId === activeSessionId) {
        if (newSessions.length > 0) {
          loadSession(newSessions[0].id);
        } else {
          setActiveSessionId(null);
          setActiveSession(null);
        }
      }
    },
    [sessions, activeSessionId, saveSessions, loadSession]
  );

  // Sessiya adını dəyiş
  const renameSession = useCallback(
    (sessionId, newName) => {
      updateSession(sessionId, {
        name: newName.trim() || PLACEHOLDERS.SESSION_NAME,
      });
    },
    [updateSession]
  );

  // Sessiya təsvirini yenilə
  const updateSessionDescription = useCallback(
    (sessionId, description) => {
      updateSession(sessionId, { description });
    },
    [updateSession]
  );

  // Sessiya dilini yenilə
  const updateSessionLanguage = useCallback(
    (sessionId, language) => {
      updateSession(sessionId, { language });
    },
    [updateSession]
  );

  // Mermaid kodunu yenilə
  const updateSessionMermaidCode = useCallback(
    (sessionId, mermaidCode, details) => {
      updateSession(sessionId, {
        mermaidCode,
        details:
          details instanceof Map
            ? details
            : new Map(Object.entries(details || {})),
      });
    },
    [updateSession]
  );

  // Söhbət tarixçəsini yenilə
  const updateConversationHistory = useCallback(
    (sessionId, history) => {
      updateSession(sessionId, { conversationHistory: history });
    },
    [updateSession]
  );

  // Söhbət mesajı əlavə et
  const addChatMessage = useCallback(
    (sessionId, message) => {
      console.log("Adding chat message:", { sessionId, message });
      const session = sessions.find((s) => s.id === sessionId);
      if (session) {
        const newHistory = [...(session.conversationHistory || []), message];
        console.log("New conversation history:", newHistory);

        // Sessiyaları yenilə
        const newSessions = sessions.map((s) =>
          s.id === sessionId
            ? { ...s, conversationHistory: newHistory, updatedAt: Date.now() }
            : s
        );

        console.log("Updated sessions:", newSessions);

        // State-i yenilə
        setSessions(newSessions);

        // Dərhal localStorage-ə saxla
        saveSessions(newSessions);

        // Aktiv sessiya da dərhal yenilənsin
        if (sessionId === activeSessionId) {
          const updatedActiveSession = {
            ...session,
            conversationHistory: newHistory,
            updatedAt: Date.now(),
          };
          console.log("Updating active session:", updatedActiveSession);
          setActiveSession(updatedActiveSession);
        }

        return newHistory; // Yenilənmiş tarixçəni qaytar
      }
      console.log("Session not found for addChatMessage:", sessionId);
      return null; // Sessiya tapılmadı
    },
    [sessions, activeSessionId, saveSessions]
  );

  // Söhbət tarixçəsini təmizlə
  const clearConversationHistory = useCallback(
    (sessionId) => {
      updateConversationHistory(sessionId, []);
    },
    [updateConversationHistory]
  );

  // Bütün sessiyaları sil
  const clearAllSessions = useCallback(() => {
    storage.remove(STORAGE_KEYS.SESSIONS);
    storage.remove(STORAGE_KEYS.ACTIVE_SESSION);
    setSessions([]);
    setActiveSessionId(null);
    setActiveSession(null);
  }, []);

  // Sessiya statistikaları
  const getSessionStats = useCallback(
    (sessionId) => {
      const session = sessions.find((s) => s.id === sessionId);
      if (!session) return null;

      return {
        messageCount: session.conversationHistory?.length || 0,
        hasFlowchart: Boolean(session.mermaidCode),
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        language: session.language,
      };
    },
    [sessions]
  );

  // İlk yükləmə
  useEffect(() => {
    console.log("Initial load effect triggered");
    loadSessions();
  }, []); // Dependency array-ı boş saxla

  // Aktiv sessiyanu yüklə - sessions yüklənəndən sonra
  useEffect(() => {
    console.log("Active session effect:", {
      activeSessionId,
      sessionsLength: sessions.length,
    });
    if (activeSessionId && sessions.length > 0) {
      const session = sessions.find((s) => s.id === activeSessionId);
      if (session) {
        console.log("Setting active session directly:", session);
        // Map obyektini yenidən yarat
        if (session.details && !(session.details instanceof Map)) {
          session.details = new Map(Object.entries(session.details));
        }
        if (!session.details) {
          session.details = new Map();
        }
        setActiveSession(session);
      }
    }
  }, [activeSessionId, sessions]);

  return {
    sessions,
    activeSessionId,
    activeSession,
    createNewSession,
    loadSession,
    updateSession,
    deleteSession,
    renameSession,
    updateSessionDescription,
    updateSessionLanguage,
    updateSessionMermaidCode,
    updateConversationHistory,
    addChatMessage,
    clearConversationHistory,
    clearAllSessions,
    getSessionStats,
    loadSessions,
  };
};
