// Proq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dilləri
export const PROGRAMMING_LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'swift', label: 'Swift' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'dart', label: 'Dart' },
  { value: 'scala', label: 'Scala' },
  { value: 'r', label: 'R' },
  { value: 'matlab', label: 'MATLAB' },
  { value: 'perl', label: 'Perl' },
  { value: 'lua', label: 'Lua' },
  { value: 'haskell', label: 'Haskell' },
  { value: 'clojure', label: 'Clojure' },
  { value: 'erlang', label: 'Erlang' },
  { value: 'elixir', label: 'Elixir' },
  { value: 'fsharp', label: 'F#' },
  { value: 'ocaml', label: 'OCaml' },
  { value: 'julia', label: 'Julia' },
  { value: 'c', label: 'C' }
];

// Mermaid konfiqurasiyası
export const MERMAID_CONFIG = {
  startOnLoad: false,
  theme: 'dark',
  themeVariables: {
    primaryColor: '#ffffff',
    primaryTextColor: '#000000',
    primaryBorderColor: '#ffffff',
    lineColor: '#ffffff',
    sectionBkgColor: '#1f1f1f',
    altSectionBkgColor: '#2c2c2c',
    gridColor: '#3a3a3a',
    secondaryColor: '#ffffff',
    tertiaryColor: '#2c2c2c',
    background: '#000000',
    mainBkg: '#1f1f1f',
    secondBkg: '#2c2c2c',
    tertiaryBkg: '#3a3a3a'
  },
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true,
    curve: 'basis'
  },
  securityLevel: 'loose'
};

// API konfiqurasiyası
export const API_CONFIG = {
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000
};

// LocalStorage açarları
export const STORAGE_KEYS = {
  API_KEY: 'gemini_api_key',
  SESSIONS: 'flowchart_sessions',
  ACTIVE_SESSION: 'active_session_id',
  PANEL_STATES: 'panel_states'
};

// UI sabitləri
export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 280,
  CHAT_WIDTH: 320,
  MIN_INPUT_HEIGHT: 60,
  MAX_INPUT_HEIGHT: 200,
  ANIMATION_DURATION: 300
};

// Xəta mesajları
export const ERROR_MESSAGES = {
  NO_API_KEY: 'API açarı tələb olunur',
  NO_INPUT: 'Zəhmət olmasa, bir təsvir daxil edin',
  API_ERROR: 'API xətası baş verdi',
  NETWORK_ERROR: 'Şəbəkə xətası',
  INVALID_RESPONSE: 'Yanlış cavab formatı',
  GENERATION_FAILED: 'Blok-sxem yaradıla bilmədi'
};

// Placeholder mətnləri
export const PLACEHOLDERS = {
  INPUT: 'Alqoritminizi təsvir edin... (məsələn: "İki ədədi toplayıb nəticəni göstərən alqoritm")',
  CHAT: 'Sualınızı yazın...',
  DIAGRAM: 'Blok-sxeminiz burada göstəriləcək',
  SESSION_NAME: 'Yeni Sessiya'
};

// Sistem prompt şablonu
export const SYSTEM_PROMPT_TEMPLATE = `
Sən Azərbaycan dilində blok-sxem yaradıcısısan. İstifadəçinin təsvirindən Mermaid.js formatında blok-sxem yaradırsan.

QAYDALAR:
1. YALNIZ Mermaid.js flowchart sintaksisini istifadə et
2. Hər node üçün click event əlavə et: click NODEID call showDetails("NODEID")
3. Hər node üçün %% şərh əlavə et ki, təfərrüatları və kod nümunələrini ehtiva etsin
4. Şərhlər formatı: %% NODEID:::Təsvir__NL__\`\`\`LANGUAGE__NL__kod__NL__\`\`\`
5. __NL__ yeni sətir üçün istifadə et
6. Node ID-ləri A1, B1, C1... formatında olsun
7. Başlanğıc node-u (("Başlanğıc")) formatında yaz
8. Son node-u (("Son")) formatında yaz

NÜMUNƏ ÇIXIŞ:
graph TD;
%% A1:::Bu addım proqramın başlanğıc nöqtəsini bildirir. Heç bir kod tələb olunmur.
%% B1:::Bu addımda istifadəçidən məlumat almaq üçün lazım olan 'mysql.connector' kitabxanasını idxal edirik.__NL__\`\`\`python__NL__import mysql.connector__NL__\`\`\`
A1(("Başlanğıc")) --> B1["Kitabxananı İdxal Et"];
click A1 call showDetails("A1")
click B1 call showDetails("B1")
`;

// Chat sistem promptu
export const CHAT_SYSTEM_PROMPT = `
Sən Azərbaycan dilində blok-sxem və proqramlaşdırma üzrə köməkçisən.

QAYDALAR:
1. YALNIZ Azərbaycan dilində cavab ver
2. Markdown formatında cavab ver
3. Kod nümunələrini \`\`\` blokları ilə əhatə et
4. YALNIZ verilmiş blok-sxem və onunla əlaqəli proqramlaşdırma mövzuları haqqında danış
5. Kənar suallara cavab verməkdən imtina et və istifadəçini nəzakətlə mövzuya qaytar

Blok-sxem konteksti: {mermaidContext}
Detallar: {details}

İstifadəçi sualı: {message}
`;
