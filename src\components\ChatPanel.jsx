import { useState, useRef, useEffect } from "react";
import { Send, X, MessageSquare, Trash2, Bo<PERSON>, User } from "lucide-react";
import { marked } from "marked";
import { PLACEHOLDERS } from "../constants";

const ChatPanel = ({
  isOpen,
  onClose,
  messages = [],
  onSendMessage,
  onClearHistory,
  loading = false,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Mesajları aşağıya scroll et
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Panel açıldıqda input-a focus et
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSend = async () => {
    const message = inputValue.trim();
    if (message && !loading && !disabled && onSendMessage) {
      // Input-u dərhal təmizlə ki, istifadəçi yeni mesaj yaza bilsin
      setInputValue("");

      try {
        await onSendMessage(message);
      } catch (error) {
        console.error("Send message error:", error);
        // Xəta olduqda mesajı geri qaytar
        setInputValue(message);
      }
    }
  };

  const handleKeyPress = async (e) => {
    if (e.key === "Enter") {
      if (e.ctrlKey || e.metaKey) {
        // Ctrl+Enter və ya Cmd+Enter ilə göndər
        e.preventDefault();
        await handleSend();
      } else if (!e.shiftKey) {
        // Sadə Enter ilə də göndər (Shift+Enter yeni sətir üçün)
        e.preventDefault();
        await handleSend();
      }
      // Shift+Enter halında heç nə etmə, yeni sətir əlavə olsun
    }
  };

  const handleClearHistory = () => {
    if (onClearHistory && !loading) {
      onClearHistory();
    }
  };

  // Markdown-ı HTML-ə çevir
  const renderMarkdown = (text) => {
    try {
      return { __html: marked(text) };
    } catch (error) {
      console.error("Markdown render error:", error);
      return { __html: text };
    }
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={onClose}
        />
      )}

      {/* Chat Panel - Always Overlay */}
      <aside
        className={`
          fixed top-0 right-0 h-full w-80 z-50
          flex flex-col transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "translate-x-full"}
        `}
        style={{
          backgroundColor: "var(--surface-1)",
          borderLeft: "1px solid var(--border-color)",
        }}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-sm font-semibold text-white">AI Köməkçi</h2>
                <p className="text-xs text-gray-400">Alqoritmi müzakirə edin</p>
              </div>
            </div>
            <div className="flex items-center gap-1">
              {messages.length > 0 && (
                <button
                  onClick={handleClearHistory}
                  disabled={loading}
                  title="Tarixçəni təmizlə"
                  className="w-8 h-8 rounded-lg hover:bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors disabled:opacity-50"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={onClose}
                className="md:hidden w-8 h-8 rounded-lg hover:bg-gray-800 flex items-center justify-center text-gray-400"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-gray-400 mt-8">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">Hələ mesaj yoxdur</p>
              <p className="text-xs mt-1">Blok-sxem haqqında sual verin</p>
            </div>
          ) : (
            messages.map((message, index) => (
              <div
                key={index}
                className={`flex gap-3 ${
                  message.role === "user" ? "flex-row-reverse" : "flex-row"
                }`}
              >
                <div
                  className={`
                  w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0
                  ${message.role === "user" ? "bg-blue-600" : "bg-gray-700"}
                `}
                >
                  {message.role === "user" ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>
                <div
                  className={`
                  flex-1 rounded-lg p-3 max-w-[calc(100%-3rem)]
                  ${
                    message.role === "user"
                      ? "bg-blue-600/20 border border-blue-500/30"
                      : "bg-gray-800/50 border border-gray-700"
                  }
                `}
                >
                  {message.role === "user" ? (
                    <p className="text-sm text-white whitespace-pre-wrap">
                      {message.content}
                    </p>
                  ) : (
                    <div
                      className="text-sm text-gray-200 prose prose-sm prose-invert max-w-none"
                      dangerouslySetInnerHTML={renderMarkdown(message.content)}
                    />
                  )}
                </div>
              </div>
            ))
          )}

          {/* Loading Message */}
          {loading && (
            <div className="flex gap-3">
              <div className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg p-3">
                <div className="flex items-center gap-2 text-gray-400">
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">Düşünürəm...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex gap-2">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                disabled ? "Əvvəlcə blok-sxem yaradın" : PLACEHOLDERS.CHAT
              }
              disabled={disabled || loading}
              rows={1}
              className="flex-1 resize-none bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ minHeight: "40px", maxHeight: "120px" }}
              onInput={(e) => {
                e.target.style.height = "auto";
                e.target.style.height =
                  Math.min(e.target.scrollHeight, 120) + "px";
              }}
            />
            <button
              onClick={handleSend}
              disabled={!inputValue.trim() || loading || disabled}
              className="w-10 h-10 bg-blue-600 hover:bg-blue-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>

          {disabled && (
            <p className="text-xs text-gray-500 mt-2 text-center">
              Chat üçün əvvəlcə blok-sxem yaradın
            </p>
          )}
        </div>
      </aside>
    </>
  );
};

export default ChatPanel;
