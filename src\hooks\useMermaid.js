import { useState, useCallback, useEffect, useRef } from "react";
import mermaid from "mermaid";
import svg<PERSON><PERSON><PERSON><PERSON> from "svg-pan-zoom";
import { MERMAID_CONFIG } from "../constants";
import { cleanMermaidCode, extractDetailsFromMermaid } from "../utils";

export const useMermaid = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const panZoomInstanceRef = useRef(null);

  // Mermaid-i başlat
  const initializeMermaid = useCallback(() => {
    if (!isInitialized) {
      try {
        mermaid.initialize({
          ...MERMAID_CONFIG,
          securityLevel: "loose",
          startOnLoad: false,
        });
        setIsInitialized(true);
        setError(null);
      } catch (err) {
        console.error("Mermaid initialization error:", err);
        setError("Mermaid başladıla bilmədi");
      }
    }
  }, [isInitialized]);

  // Diaqram render et
  const renderDiagram = useCallback(
    async (mermaidCode, containerElement) => {
      if (!mermaidCode || !mermaidCode.trim()) {
        setError("Mermaid kodu boşdur");
        return null;
      }

      if (!containerElement) {
        setError("Container elementi tapılmadı");
        return null;
      }

      setLoading(true);
      setError(null);

      try {
        const cleanCode = cleanMermaidCode(mermaidCode);

        // Mermaid-i başlat (əgər başladılmayıbsa)
        if (!isInitialized) {
          initializeMermaid();
        }

        // Köhnə diaqramı təmizlə
        try {
          // Pan-zoom instance-ni təmizlə
          if (panZoomInstanceRef.current) {
            panZoomInstanceRef.current.destroy();
            panZoomInstanceRef.current = null;
          }

          // Container-i təmizlə - React-friendly yolla
          if (containerElement) {
            // Mermaid wrapper div yarat və ya tap
            let mermaidWrapper =
              containerElement.querySelector(".mermaid-wrapper");
            if (mermaidWrapper) {
              containerElement.removeChild(mermaidWrapper);
            }
          }
        } catch (cleanupError) {
          console.warn("Cleanup error:", cleanupError);
          // Fallback - React-in idarə etmədiyi wrapper istifadə et
          try {
            const existingWrapper =
              containerElement.querySelector(".mermaid-wrapper");
            if (existingWrapper) {
              containerElement.removeChild(existingWrapper);
            }
          } catch (fallbackError) {
            console.warn("Fallback cleanup error:", fallbackError);
          }
        }

        // Unique ID yarat
        const uniqueId = `mermaid-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 11)}`;

        // Yeni diaqram render et
        const { svg } = await mermaid.render(uniqueId, cleanCode);

        // Mermaid wrapper div yarat
        const mermaidWrapper = document.createElement("div");
        mermaidWrapper.className = "mermaid-wrapper w-full h-full";
        mermaidWrapper.innerHTML = svg;

        const svgElement = mermaidWrapper.querySelector("svg");

        if (svgElement && containerElement) {
          // Wrapper-i container-ə əlavə et
          containerElement.appendChild(mermaidWrapper);

          // SVG-ni responsive et
          if (svgElement) {
            svgElement.style.width = "100%";
            svgElement.style.height = "100%";
            svgElement.style.maxWidth = "none";
            svgElement.style.maxHeight = "none";

            // Pan & Zoom əlavə et
            setTimeout(() => {
              try {
                // Köhnə pan-zoom instance-ni təmizlə
                if (panZoomInstanceRef.current) {
                  panZoomInstanceRef.current.destroy();
                  panZoomInstanceRef.current = null;
                }

                // Yeni pan-zoom instance yarat
                panZoomInstanceRef.current = svgPanZoom(svgElement, {
                  zoomEnabled: true,
                  controlIconsEnabled: false,
                  fit: true,
                  center: true,
                  minZoom: 0.1,
                  maxZoom: 10,
                  zoomScaleSensitivity: 0.2,
                  dblClickZoomEnabled: true,
                  mouseWheelZoomEnabled: true,
                  preventMouseEventsDefault: true,
                  beforeZoom: () => true,
                  onZoom: () => {},
                  beforePan: () => true,
                  onPan: () => {},
                  customEventsHandler: {
                    haltEventListeners: [
                      "touchstart",
                      "touchend",
                      "touchmove",
                      "touchleave",
                      "touchcancel",
                    ],
                    init: (options) => {
                      const instance = options.instance;
                      let hammer;

                      if (window.Hammer) {
                        hammer = new window.Hammer(options.svgElement);
                        hammer.get("pinch").set({ enable: true });

                        hammer.on("pinch", (ev) => {
                          ev.preventDefault();
                          instance.zoomAtPoint(ev.scale, {
                            x: ev.center.x,
                            y: ev.center.y,
                          });
                        });

                        hammer.on("pan", (ev) => {
                          ev.preventDefault();
                          instance.panBy({ x: ev.deltaX, y: ev.deltaY });
                        });
                      }
                    },
                    destroy: () => {},
                  },
                });
              } catch (panZoomError) {
                console.warn("Pan-zoom initialization failed:", panZoomError);
              }
            }, 100);
          }
        }

        // Click event-lərini əlavə et
        setTimeout(() => {
          const svgElement = containerElement.querySelector("svg");
          if (svgElement) {
            // Bütün node-lara click event əlavə et
            const nodes = svgElement.querySelectorAll(
              '.node, .flowchart-node, [id*="flowchart-"]'
            );
            nodes.forEach((node, index) => {
              node.style.cursor = "pointer";
              node.addEventListener("click", (e) => {
                e.preventDefault();
                e.stopPropagation();
                const nodeId = node.id || `node-${index}`;
                if (window.showDetails) {
                  window.showDetails(nodeId);
                }
              });
            });
          }
        }, 200);

        // Detalları çıxar
        const details = extractDetailsFromMermaid(cleanCode);

        setLoading(false);
        return { svg, details };
      } catch (err) {
        console.error("Mermaid render error:", err);
        setError("Diaqram render edilə bilmədi: " + err.message);
        setLoading(false);
        return null;
      }
    },
    [isInitialized, initializeMermaid]
  );

  // Zoom funksiyaları
  const zoomIn = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.zoomIn();
      } catch (error) {
        console.warn("Zoom in error:", error);
      }
    }
  }, []);

  const zoomOut = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.zoomOut();
      } catch (error) {
        console.warn("Zoom out error:", error);
      }
    }
  }, []);

  const resetZoom = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.resetZoom();
        panZoomInstanceRef.current.center();
      } catch (error) {
        console.warn("Reset zoom error:", error);
      }
    }
  }, []);

  const fitToScreen = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.fit();
        panZoomInstanceRef.current.center();
      } catch (error) {
        console.warn("Fit to screen error:", error);
      }
    }
  }, []);

  // Pan-zoom instance-ni təmizlə
  const destroyPanZoom = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.destroy();
        panZoomInstanceRef.current = null;
      } catch (error) {
        console.warn("Destroy pan-zoom error:", error);
      }
    }
  }, []);

  // Diaqramı təmizlə
  const clearDiagram = useCallback(
    (containerElement) => {
      if (containerElement) {
        try {
          // Pan-zoom instance-ni təmizlə
          destroyPanZoom();

          // Yalnız mermaid wrapper-i sil
          const mermaidWrapper =
            containerElement.querySelector(".mermaid-wrapper");
          if (mermaidWrapper) {
            containerElement.removeChild(mermaidWrapper);
          }

          // Boş mesaj əlavə etmə - FlowchartDisplay özü göstərəcək
        } catch (error) {
          console.warn("Clear diagram error:", error);
          // Fallback - bütün uşaq elementləri sil
          try {
            while (containerElement.firstChild) {
              containerElement.removeChild(containerElement.firstChild);
            }
            // Boş mesaj əlavə etmə - FlowchartDisplay özü göstərəcək
          } catch (fallbackError) {
            console.warn("Fallback clear error:", fallbackError);
            containerElement.innerHTML =
              '<p class="text-gray-400">Blok-sxeminiz burada göstəriləcək</p>';
          }
        }
      }
      setError(null);
    },
    [destroyPanZoom]
  );

  // Xətanı təmizlə
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Component unmount olduqda təmizlə
  useEffect(() => {
    return () => {
      destroyPanZoom();
    };
  }, [destroyPanZoom]);

  // İlk başlatma
  useEffect(() => {
    initializeMermaid();
  }, [initializeMermaid]);

  return {
    isInitialized,
    loading,
    error,
    renderDiagram,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToScreen,
    clearDiagram,
    clearError,
    panZoomInstance: panZoomInstanceRef.current,
  };
};
