import { useState, useCallback, useEffect, useRef } from "react";
import mermaid from "mermaid";
import svg<PERSON><PERSON><PERSON><PERSON> from "svg-pan-zoom";
import { MERMAID_CONFIG } from "../constants";
import { cleanMermaidCode, extractDetailsFromMermaid } from "../utils";

export const useMermaid = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const panZoomInstanceRef = useRef(null);
  const containerRef = useRef(null);

  // Mermaid-i başlat
  const initializeMermaid = useCallback(() => {
    if (!isInitialized) {
      try {
        mermaid.initialize(MERMAID_CONFIG);
        setIsInitialized(true);
        setError(null);
      } catch (err) {
        console.error("Mermaid initialization error:", err);
        setError("Mermaid başlad<PERSON>la bilmədi");
      }
    }
  }, [isInitialized]);

  // Diaqram render et
  const renderDiagram = useCallback(
    async (mermaidCode, containerElement) => {
      if (!mermaidCode || !mermaidCode.trim()) {
        setError("Mermaid kodu boşdur");
        return null;
      }

      if (!containerElement) {
        setError("Container elementi tapılmadı");
        return null;
      }

      setLoading(true);
      setError(null);

      try {
        const cleanCode = cleanMermaidCode(mermaidCode);

        // Mermaid-i başlat (əgər başladılmayıbsa)
        if (!isInitialized) {
          initializeMermaid();
        }

        // Köhnə diaqramı təmizlə
        try {
          // Pan-zoom instance-ni təmizlə
          if (panZoomInstanceRef.current) {
            panZoomInstanceRef.current.destroy();
            panZoomInstanceRef.current = null;
          }

          // Container-i təmizlə
          while (containerElement.firstChild) {
            containerElement.removeChild(containerElement.firstChild);
          }
        } catch (cleanupError) {
          console.warn("Cleanup error:", cleanupError);
          // Fallback
          containerElement.innerHTML = "";
        }

        // Unique ID yarat
        const uniqueId = `mermaid-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 11)}`;

        // Yeni diaqram render et
        const { svg } = await mermaid.render(uniqueId, cleanCode);

        // SVG-ni container-ə əlavə et
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = svg;
        const svgElement = tempDiv.querySelector("svg");

        if (svgElement && containerElement) {
          containerElement.appendChild(svgElement);

          // SVG-ni responsive et
          if (svgElement) {
            // SVG-ni responsive et
            svgElement.style.width = "100%";
            svgElement.style.height = "100%";
            svgElement.style.maxWidth = "none";
            svgElement.style.maxHeight = "none";

            // Pan & Zoom əlavə et
            setTimeout(() => {
              try {
                // Köhnə pan-zoom instance-ni təmizlə
                if (panZoomInstanceRef.current) {
                  panZoomInstanceRef.current.destroy();
                  panZoomInstanceRef.current = null;
                }

                // Yeni pan-zoom instance yarat
                panZoomInstanceRef.current = svgPanZoom(svgElement, {
                  zoomEnabled: true,
                  controlIconsEnabled: false,
                  fit: true,
                  center: true,
                  minZoom: 0.1,
                  maxZoom: 10,
                  zoomScaleSensitivity: 0.2,
                  dblClickZoomEnabled: true,
                  mouseWheelZoomEnabled: true,
                  preventMouseEventsDefault: true,
                  beforeZoom: () => true,
                  onZoom: () => {},
                  beforePan: () => true,
                  onPan: () => {},
                  customEventsHandler: {
                    haltEventListeners: [
                      "touchstart",
                      "touchend",
                      "touchmove",
                      "touchleave",
                      "touchcancel",
                    ],
                    init: (options) => {
                      const instance = options.instance;
                      let hammer;

                      if (window.Hammer) {
                        hammer = new window.Hammer(options.svgElement);
                        hammer.get("pinch").set({ enable: true });

                        hammer.on("pinch", (ev) => {
                          ev.preventDefault();
                          instance.zoomAtPoint(ev.scale, {
                            x: ev.center.x,
                            y: ev.center.y,
                          });
                        });

                        hammer.on("pan", (ev) => {
                          ev.preventDefault();
                          instance.panBy({ x: ev.deltaX, y: ev.deltaY });
                        });
                      }
                    },
                    destroy: () => {},
                  },
                });
              } catch (panZoomError) {
                console.warn("Pan-zoom initialization failed:", panZoomError);
              }
            }, 100);
          }
        }

        // Detalları çıxar
        const details = extractDetailsFromMermaid(cleanCode);

        setLoading(false);
        return { svg, details };
      } catch (err) {
        console.error("Mermaid render error:", err);
        setError("Diaqram render edilə bilmədi: " + err.message);
        setLoading(false);
        return null;
      }
    },
    [isInitialized, initializeMermaid]
  );

  // Zoom funksiyaları
  const zoomIn = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.zoomIn();
      } catch (error) {
        console.warn("Zoom in error:", error);
      }
    }
  }, []);

  const zoomOut = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.zoomOut();
      } catch (error) {
        console.warn("Zoom out error:", error);
      }
    }
  }, []);

  const resetZoom = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.resetZoom();
        panZoomInstanceRef.current.center();
      } catch (error) {
        console.warn("Reset zoom error:", error);
      }
    }
  }, []);

  const fitToScreen = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.fit();
        panZoomInstanceRef.current.center();
      } catch (error) {
        console.warn("Fit to screen error:", error);
      }
    }
  }, []);

  // Pan-zoom instance-ni təmizlə
  const destroyPanZoom = useCallback(() => {
    if (panZoomInstanceRef.current) {
      try {
        panZoomInstanceRef.current.destroy();
        panZoomInstanceRef.current = null;
      } catch (error) {
        console.warn("Destroy pan-zoom error:", error);
      }
    }
  }, []);

  // Diaqramı təmizlə
  const clearDiagram = useCallback(
    (containerElement) => {
      if (containerElement) {
        try {
          while (containerElement.firstChild) {
            containerElement.removeChild(containerElement.firstChild);
          }
          const emptyMessage = document.createElement("p");
          emptyMessage.className = "text-gray-400";
          emptyMessage.textContent = "Blok-sxeminiz burada göstəriləcək";
          containerElement.appendChild(emptyMessage);
        } catch (error) {
          console.warn("Clear diagram error:", error);
          containerElement.innerHTML =
            '<p class="text-gray-400">Blok-sxeminiz burada göstəriləcək</p>';
        }
      }
      destroyPanZoom();
      setError(null);
    },
    [destroyPanZoom]
  );

  // Xətanı təmizlə
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Component unmount olduqda təmizlə
  useEffect(() => {
    return () => {
      destroyPanZoom();
    };
  }, [destroyPanZoom]);

  // İlk başlatma
  useEffect(() => {
    initializeMermaid();
  }, [initializeMermaid]);

  return {
    isInitialized,
    loading,
    error,
    renderDiagram,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToScreen,
    clearDiagram,
    clearError,
    panZoomInstance: panZoomInstanceRef.current,
  };
};
